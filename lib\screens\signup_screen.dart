import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/mock_auth_service.dart';
import '../models/auth_state.dart';
import '../widgets/secure_password_field.dart';
import 'login_screen.dart';
import 'terms_of_use_screen.dart';
import 'privacy_policy_screen.dart';

/// 📝 Secure Signup Screen for CHICA'S Chicken
/// Implements comprehensive validation, security measures, and progress tracking
class SignupScreen extends StatefulWidget {
  const SignupScreen({Key? key}) : super(key: key);

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  final _nameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _phoneFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();
  
  late AnimationController _animationController;
  late AnimationController _progressController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  bool _isLoading = false;
  bool _acceptTerms = false;
  bool _acceptPrivacy = false;
  bool _subscribeNewsletter = true;
  String? _errorMessage;
  PasswordValidationResult _passwordValidation = PasswordValidationResult.invalid(errors: []);
  
  // Progress tracking
  int _currentStep = 0;
  final int _totalSteps = 4;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _progressController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nameFocusNode.dispose();
    _emailFocusNode.dispose();
    _phoneFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  Future<void> _handleSignup() async {
    if (!_formKey.currentState!.validate()) return;
    
    if (!_acceptTerms || !_acceptPrivacy) {
      _showErrorSnackBar('Please accept the Terms of Use and Privacy Policy to continue');
      return;
    }
    
    if (!_passwordValidation.isValid) {
      _showErrorSnackBar('Please ensure your password meets all requirements');
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    _progressController.forward();

    try {
      final authService = Provider.of<MockAuthService>(context, listen: false);
      final result = await authService.signUpWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
      );

      if (result.hasError) {
        setState(() {
          _errorMessage = result.errorMessage;
        });
        _showErrorSnackBar(result.errorMessage!);
      } else if (result.isAuthenticated) {
        _showSuccessDialog();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Signup failed. Please try again.';
      });
      _showErrorSnackBar('Signup failed. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _progressController.reset();
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 12),
            Text('Welcome to CHICA\'S Chicken!'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Your account has been created successfully!'),
            SizedBox(height: 12),
            Text('🎉 You\'ve earned 100 welcome bonus points!'),
            SizedBox(height: 8),
            Text('📧 Please check your email to verify your account.'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const LoginScreen()),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF5C22),
              foregroundColor: Colors.white,
            ),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red[600],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _updateProgress() {
    int completedSteps = 0;
    
    if (_nameController.text.isNotEmpty && _emailController.text.isNotEmpty) {
      completedSteps++;
    }
    if (_passwordValidation.isValid) {
      completedSteps++;
    }
    if (_passwordController.text == _confirmPasswordController.text && 
        _confirmPasswordController.text.isNotEmpty) {
      completedSteps++;
    }
    if (_acceptTerms && _acceptPrivacy) {
      completedSteps++;
    }
    
    setState(() {
      _currentStep = completedSteps;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Create Account',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Progress Indicator
                    _buildProgressIndicator(),
                    
                    const SizedBox(height: 32),
                    
                    // Header
                    _buildHeader(),
                    
                    const SizedBox(height: 32),
                    
                    // Name Field
                    _buildNameField(),
                    
                    const SizedBox(height: 20),
                    
                    // Email Field
                    _buildEmailField(),
                    
                    const SizedBox(height: 20),
                    
                    // Phone Field (Optional)
                    _buildPhoneField(),
                    
                    const SizedBox(height: 20),
                    
                    // Password Field
                    _buildPasswordField(),
                    
                    const SizedBox(height: 20),
                    
                    // Confirm Password Field
                    _buildConfirmPasswordField(),
                    
                    const SizedBox(height: 24),
                    
                    // Newsletter Subscription
                    _buildNewsletterOption(),
                    
                    const SizedBox(height: 16),
                    
                    // Terms and Privacy Checkboxes
                    _buildLegalAgreements(),
                    
                    const SizedBox(height: 32),
                    
                    // Signup Button
                    _buildSignupButton(),
                    
                    if (_isLoading) ...[
                      const SizedBox(height: 16),
                      _buildLoadingIndicator(),
                    ],
                    
                    const SizedBox(height: 24),
                    
                    // Login Link
                    _buildLoginLink(),
                    
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Step $_currentStep of $_totalSteps',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${((_currentStep / _totalSteps) * 100).round()}% Complete',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: const Color(0xFFFF5C22),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: _currentStep / _totalSteps,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFFF5C22)),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'Join CHICA\'S Chicken',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Create your account and start earning rewards!',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      focusNode: _nameFocusNode,
      textInputAction: TextInputAction.next,
      enabled: !_isLoading,
      textCapitalization: TextCapitalization.words,
      onFieldSubmitted: (_) => _emailFocusNode.requestFocus(),
      onChanged: (_) => _updateProgress(),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter your full name';
        }
        if (value.trim().length < 2) {
          return 'Name must be at least 2 characters long';
        }
        return null;
      },
      decoration: InputDecoration(
        labelText: 'Full Name *',
        hintText: 'Enter your full name',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFF5C22), width: 2),
        ),
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      focusNode: _emailFocusNode,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      enabled: !_isLoading,
      onFieldSubmitted: (_) => _phoneFocusNode.requestFocus(),
      onChanged: (_) => _updateProgress(),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your email address';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'Please enter a valid email address';
        }
        return null;
      },
      decoration: InputDecoration(
        labelText: 'Email Address *',
        hintText: 'Enter your email',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFF5C22), width: 2),
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    return TextFormField(
      controller: _phoneController,
      focusNode: _phoneFocusNode,
      keyboardType: TextInputType.phone,
      textInputAction: TextInputAction.next,
      enabled: !_isLoading,
      onFieldSubmitted: (_) => _passwordFocusNode.requestFocus(),
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10),
      ],
      validator: (value) {
        if (value != null && value.isNotEmpty && value.length < 10) {
          return 'Please enter a valid phone number';
        }
        return null;
      },
      decoration: InputDecoration(
        labelText: 'Phone Number (Optional)',
        hintText: 'Enter your phone number',
        prefixIcon: const Icon(Icons.phone_outlined),
        prefixText: '+1 ',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFF5C22), width: 2),
        ),
      ),
    );
  }

  Widget _buildPasswordField() {
    return SecurePasswordField(
      controller: _passwordController,
      focusNode: _passwordFocusNode,
      labelText: 'Password *',
      hintText: 'Create a strong password',
      enabled: !_isLoading,
      textInputAction: TextInputAction.next,
      onFieldSubmitted: (_) => _confirmPasswordFocusNode.requestFocus(),
      onValidationChanged: (result) {
        setState(() {
          _passwordValidation = result;
        });
        _updateProgress();
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a password';
        }
        if (!_passwordValidation.isValid) {
          return 'Password does not meet requirements';
        }
        return null;
      },
    );
  }

  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      focusNode: _confirmPasswordFocusNode,
      obscureText: true,
      textInputAction: TextInputAction.done,
      enabled: !_isLoading,
      onFieldSubmitted: (_) => _handleSignup(),
      onChanged: (_) => _updateProgress(),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please confirm your password';
        }
        if (value != _passwordController.text) {
          return 'Passwords do not match';
        }
        return null;
      },
      decoration: InputDecoration(
        labelText: 'Confirm Password *',
        hintText: 'Re-enter your password',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: _passwordController.text.isNotEmpty && 
                   _confirmPasswordController.text.isNotEmpty
            ? Icon(
                _passwordController.text == _confirmPasswordController.text
                    ? Icons.check_circle
                    : Icons.error,
                color: _passwordController.text == _confirmPasswordController.text
                    ? Colors.green
                    : Colors.red,
              )
            : null,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFF5C22), width: 2),
        ),
      ),
    );
  }

  Widget _buildNewsletterOption() {
    return Row(
      children: [
        Checkbox(
          value: _subscribeNewsletter,
          onChanged: _isLoading ? null : (value) {
            setState(() {
              _subscribeNewsletter = value ?? false;
            });
          },
          activeColor: const Color(0xFFFF5C22),
        ),
        Expanded(
          child: Text(
            'Subscribe to our newsletter for exclusive offers and updates',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildLegalAgreements() {
    return Column(
      children: [
        // Terms of Use
        Row(
          children: [
            Checkbox(
              value: _acceptTerms,
              onChanged: _isLoading ? null : (value) {
                setState(() {
                  _acceptTerms = value ?? false;
                });
                _updateProgress();
              },
              activeColor: const Color(0xFFFF5C22),
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TermsOfUseScreen(),
                    ),
                  );
                },
                child: RichText(
                  text: TextSpan(
                    style: Theme.of(context).textTheme.bodyMedium,
                    children: const [
                      TextSpan(text: 'I agree to the '),
                      TextSpan(
                        text: 'Terms of Use',
                        style: TextStyle(
                          color: Color(0xFFFF5C22),
                          decoration: TextDecoration.underline,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextSpan(text: ' *'),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        
        // Privacy Policy
        Row(
          children: [
            Checkbox(
              value: _acceptPrivacy,
              onChanged: _isLoading ? null : (value) {
                setState(() {
                  _acceptPrivacy = value ?? false;
                });
                _updateProgress();
              },
              activeColor: const Color(0xFFFF5C22),
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PrivacyPolicyScreen(),
                    ),
                  );
                },
                child: RichText(
                  text: TextSpan(
                    style: Theme.of(context).textTheme.bodyMedium,
                    children: const [
                      TextSpan(text: 'I agree to the '),
                      TextSpan(
                        text: 'Privacy Policy',
                        style: TextStyle(
                          color: Color(0xFFFF5C22),
                          decoration: TextDecoration.underline,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextSpan(text: ' *'),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSignupButton() {
    final isFormValid = _currentStep == _totalSteps && !_isLoading;
    
    return ElevatedButton(
      onPressed: isFormValid ? _handleSignup : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: isFormValid ? const Color(0xFFFF5C22) : Colors.grey[400],
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: isFormValid ? 2 : 0,
      ),
      child: _isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Text(
              'CREATE ACCOUNT',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                letterSpacing: 1,
              ),
            ),
    );
  }

  Widget _buildLoadingIndicator() {
    return AnimatedBuilder(
      animation: _progressController,
      builder: (context, child) {
        return LinearProgressIndicator(
          value: _progressController.value,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFFF5C22)),
        );
      },
    );
  }

  Widget _buildLoginLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Already have an account? ',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        TextButton(
          onPressed: _isLoading ? null : () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const LoginScreen()),
            );
          },
          child: Text(
            'Sign In',
            style: TextStyle(
              color: _isLoading ? Colors.grey : const Color(0xFFFF5C22),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
